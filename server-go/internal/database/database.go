package database

import (
	"fmt"
	"weishi-server/internal/config"
	"weishi-server/internal/model"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func New(cfg config.DatabaseConfig) (*gorm.DB, error) {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=%s&parseTime=True&loc=Local",
		cfg.Username,
		cfg.Password,
		cfg.Host,
		cfg.Port,
		cfg.Database,
		cfg.Charset,
	)

	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// 自动迁移数据库表
	if err := autoMigrate(db); err != nil {
		return nil, fmt.Errorf("failed to migrate database: %w", err)
	}

	return db, nil
}

func autoMigrate(db *gorm.DB) error {
	return db.AutoMigrate(
		// 用户相关
		// &model.User{},
		&model.AdminUser{},
		&model.AdminRole{},
		&model.AdminPermission{},
		&model.AdminUserRole{},
		&model.AdminRolePermission{},
		&model.AdminLog{},

		// 内容管理
		&model.Swiper{},
		&model.News{},
		&model.ProjectCase{},
		&model.Partner{},
		&model.FriendLink{},
		&model.Recruitment{},
		&model.PartPlatform{},
	)
}
