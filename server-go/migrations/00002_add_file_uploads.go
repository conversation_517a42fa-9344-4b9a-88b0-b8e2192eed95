package migrations

import (
	"database/sql"
	"fmt"

	"github.com/pressly/goose/v3"
)

func init() {
	goose.AddMigration(upAddFileUploads, downAddFileUploads)
}

// upAddFileUploads 添加文件上传相关功能
func upAddFileUploads(tx *sql.Tx) error {
	// 检查文件上传表是否已存在
	var count int
	err := tx.QueryRow("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'file_uploads'").Scan(&count)
	if err != nil {
		return fmt.Errorf("failed to check file_uploads table existence: %w", err)
	}

	// 如果表已存在，检查是否需要添加新字段
	if count > 0 {
		// 检查是否存在 uploaded_by 字段
		var columnCount int
		err = tx.QueryRow("SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'file_uploads' AND column_name = 'uploaded_by'").Scan(&columnCount)
		if err != nil {
			return fmt.Errorf("failed to check uploaded_by column: %w", err)
		}

		// 如果字段不存在，添加它
		if columnCount == 0 {
			_, err = tx.Exec("ALTER TABLE `file_uploads` ADD COLUMN `uploaded_by` bigint unsigned NOT NULL DEFAULT 0")
			if err != nil {
				return fmt.Errorf("failed to add uploaded_by column: %w", err)
			}
		}

		// 检查是否存在索引
		var indexCount int
		err = tx.QueryRow("SELECT COUNT(*) FROM information_schema.statistics WHERE table_schema = DATABASE() AND table_name = 'file_uploads' AND index_name = 'idx_file_uploads_uploaded_by'").Scan(&indexCount)
		if err != nil {
			return fmt.Errorf("failed to check index: %w", err)
		}

		// 如果索引不存在，创建它
		if indexCount == 0 {
			_, err = tx.Exec("CREATE INDEX `idx_file_uploads_uploaded_by` ON `file_uploads` (`uploaded_by`)")
			if err != nil {
				return fmt.Errorf("failed to create index: %w", err)
			}
		}
	}

	// 创建文件上传日志表（如果不存在）
	_, err = tx.Exec(`
		CREATE TABLE IF NOT EXISTS file_upload_logs (
			id bigint unsigned NOT NULL AUTO_INCREMENT,
			created_at datetime(3) DEFAULT NULL,
			updated_at datetime(3) DEFAULT NULL,
			file_upload_id bigint unsigned NOT NULL,
			action varchar(50) NOT NULL,
			user_id bigint unsigned DEFAULT NULL,
			ip_address varchar(45) DEFAULT NULL,
			user_agent text,
			details json DEFAULT NULL,
			PRIMARY KEY (id),
			KEY idx_file_upload_logs_file_upload_id (file_upload_id),
			KEY idx_file_upload_logs_action (action),
			KEY idx_file_upload_logs_created_at (created_at)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
	`)
	if err != nil {
		return fmt.Errorf("failed to create file_upload_logs table: %w", err)
	}

	return nil
}

// downAddFileUploads 回滚文件上传相关功能
func downAddFileUploads(tx *sql.Tx) error {
	// 删除文件上传日志表
	_, err := tx.Exec("DROP TABLE IF EXISTS `file_upload_logs`")
	if err != nil {
		return fmt.Errorf("failed to drop file_upload_logs table: %w", err)
	}

	// 检查file_uploads表是否存在
	var count int
	err = tx.QueryRow("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'file_uploads'").Scan(&count)
	if err != nil {
		return fmt.Errorf("failed to check file_uploads table existence: %w", err)
	}

	if count > 0 {
		// 删除索引（如果存在）
		_, err = tx.Exec("DROP INDEX IF EXISTS `idx_file_uploads_uploaded_by` ON `file_uploads`")
		if err != nil {
			return fmt.Errorf("failed to drop index: %w", err)
		}

		// 注意：这里不删除uploaded_by字段，因为可能有数据依赖
		// 在生产环境中，字段的删除需要更谨慎的处理
	}

	return nil
}
